#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级数据库查询示例

展示数据库版本相比Excel版本的独特优势
"""

import sqlite3
from gpio_af_database import GPIOAFDatabase
import os

def advanced_sql_queries():
    """演示高级SQL查询功能"""
    print("=== 高级SQL查询示例 ===")
    
    with GPIOAFDatabase() as db:
        # 确保数据已导入
        if os.path.exists("../excel/Q800_GPIO_AF.xlsx"):
            db.import_from_excel("Q800_GPIO_AF.xlsx")
        
        cursor = db.conn.cursor()
        
        print("\n1. 统计每个AF的使用频率:")
        cursor.execute('''
            SELECT af_name, COUNT(*) as usage_count 
            FROM gpio_af_config 
            GROUP BY af_name 
            ORDER BY usage_count DESC
        ''')
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]} 个配置")
        
        print("\n2. 查找多功能引脚（AF配置最多的引脚）:")
        cursor.execute('''
            SELECT pin_name, COUNT(*) as af_count 
            FROM gpio_af_config 
            GROUP BY pin_name 
            ORDER BY af_count DESC 
            LIMIT 10
        ''')
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]} 个AF配置")
        
        print("\n3. 查找共享功能（多个引脚支持的相同功能）:")
        cursor.execute('''
            SELECT usage, COUNT(DISTINCT pin_name) as pin_count 
            FROM gpio_af_config 
            GROUP BY usage 
            HAVING pin_count > 1 
            ORDER BY pin_count DESC 
            LIMIT 10
        ''')
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]} 个引脚支持")
        
        print("\n4. 查找特定端口的所有引脚配置（如PA端口）:")
        cursor.execute('''
            SELECT pin_name, COUNT(*) as af_count 
            FROM gpio_af_config 
            WHERE pin_name LIKE 'PA%' 
            GROUP BY pin_name 
            ORDER BY pin_name
        ''')
        
        pa_pins = cursor.fetchall()
        print(f"   PA端口共有 {len(pa_pins)} 个引脚:")
        for row in pa_pins[:5]:  # 只显示前5个
            print(f"     {row[0]}: {row[1]} 个AF配置")
        if len(pa_pins) > 5:
            print(f"     ... 还有 {len(pa_pins) - 5} 个引脚")
        
        print("\n5. 查找TIMER相关的所有配置:")
        cursor.execute('''
            SELECT af_name, COUNT(*) as timer_count 
            FROM gpio_af_config 
            WHERE usage LIKE '%TIMER%' 
            GROUP BY af_name 
            ORDER BY timer_count DESC
        ''')
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]} 个TIMER相关配置")
        
        print("\n6. 查找冲突检测（同一引脚的不同AF是否有相似功能）:")
        cursor.execute('''
            SELECT pin_name, GROUP_CONCAT(af_name || ':' || usage, '; ') as configs
            FROM gpio_af_config 
            WHERE pin_name IN (
                SELECT pin_name 
                FROM gpio_af_config 
                WHERE usage LIKE '%USART%' OR usage LIKE '%UART%'
                GROUP BY pin_name 
                HAVING COUNT(*) > 1
            )
            AND (usage LIKE '%USART%' OR usage LIKE '%UART%')
            GROUP BY pin_name
            LIMIT 5
        ''')
        
        print("   支持多个UART/USART功能的引脚:")
        for row in cursor.fetchall():
            print(f"     {row[0]}: {row[1]}")

def custom_analysis():
    """自定义分析功能"""
    print("\n=== 自定义分析功能 ===")
    
    with GPIOAFDatabase() as db:
        # 确保数据已导入
        if os.path.exists("../excel/Q800_GPIO_AF.xlsx"):
            db.import_from_excel("Q800_GPIO_AF.xlsx")
        
        print("\n1. 引脚功能分类统计:")
        
        # 按功能类型分类
        function_categories = {
            'TIMER': '%TIMER%',
            'USART/UART': '%USART%',
            'SPI': '%SPI%',
            'I2C': '%I2C%',
            'CAN': '%CAN%',
            'USB': '%USB%',
            'ETH': '%ETH%',
            'ADC': '%ADC%',
            'DAC': '%DAC%'
        }
        
        cursor = db.conn.cursor()
        
        for category, pattern in function_categories.items():
            cursor.execute('''
                SELECT COUNT(DISTINCT pin_name) as pin_count,
                       COUNT(*) as config_count
                FROM gpio_af_config 
                WHERE usage LIKE ?
            ''', (pattern,))
            
            result = cursor.fetchone()
            if result and result[1] > 0:
                print(f"   {category:12}: {result[0]:3} 个引脚, {result[1]:3} 个配置")
        
        print("\n2. AF利用率分析:")
        cursor.execute('''
            SELECT af_name,
                   COUNT(*) as total_configs,
                   COUNT(DISTINCT pin_name) as unique_pins,
                   ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM gpio_af_config), 2) as percentage
            FROM gpio_af_config 
            GROUP BY af_name 
            ORDER BY total_configs DESC
        ''')
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]:3} 配置 ({row[3]:5.1f}%), {row[2]:3} 个引脚")
        
        print("\n3. 端口分布分析:")
        cursor.execute('''
            SELECT SUBSTR(pin_name, 1, 2) as port,
                   COUNT(DISTINCT pin_name) as pin_count,
                   COUNT(*) as total_configs,
                   ROUND(AVG(af_count), 1) as avg_af_per_pin
            FROM (
                SELECT pin_name, COUNT(*) as af_count
                FROM gpio_af_config
                GROUP BY pin_name
            ) 
            GROUP BY SUBSTR(pin_name, 1, 2)
            ORDER BY pin_count DESC
        ''')
        
        for row in cursor.fetchall():
            print(f"   {row[0]} 端口: {row[1]:2} 个引脚, {row[2]:3} 个配置, 平均 {row[3]} AF/引脚")

def create_custom_views():
    """创建自定义视图"""
    print("\n=== 创建自定义视图 ===")
    
    with GPIOAFDatabase() as db:
        # 确保数据已导入
        if os.path.exists("../excel/Q800_GPIO_AF.xlsx"):
            db.import_from_excel("Q800_GPIO_AF.xlsx")
        
        cursor = db.conn.cursor()
        
        # 创建TIMER功能视图
        cursor.execute('''
            CREATE VIEW IF NOT EXISTS timer_functions AS
            SELECT pin_name, af_name, usage,
                   CASE 
                       WHEN usage LIKE '%TIMER0%' THEN 'TIMER0'
                       WHEN usage LIKE '%TIMER1%' THEN 'TIMER1'
                       WHEN usage LIKE '%TIMER2%' THEN 'TIMER2'
                       WHEN usage LIKE '%TIMER3%' THEN 'TIMER3'
                       WHEN usage LIKE '%TIMER4%' THEN 'TIMER4'
                       WHEN usage LIKE '%TIMER5%' THEN 'TIMER5'
                       WHEN usage LIKE '%TIMER6%' THEN 'TIMER6'
                       WHEN usage LIKE '%TIMER7%' THEN 'TIMER7'
                       WHEN usage LIKE '%TIMER14%' THEN 'TIMER14'
                       ELSE 'OTHER_TIMER'
                   END as timer_type
            FROM gpio_af_config
            WHERE usage LIKE '%TIMER%'
        ''')
        
        print("1. TIMER功能分类视图:")
        cursor.execute('''
            SELECT timer_type, COUNT(*) as count
            FROM timer_functions
            GROUP BY timer_type
            ORDER BY count DESC
        ''')
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]} 个配置")
        
        # 创建通信接口视图
        cursor.execute('''
            CREATE VIEW IF NOT EXISTS communication_interfaces AS
            SELECT pin_name, af_name, usage,
                   CASE 
                       WHEN usage LIKE '%USART%' OR usage LIKE '%UART%' THEN 'UART/USART'
                       WHEN usage LIKE '%SPI%' THEN 'SPI'
                       WHEN usage LIKE '%I2C%' THEN 'I2C'
                       WHEN usage LIKE '%CAN%' THEN 'CAN'
                       WHEN usage LIKE '%USB%' THEN 'USB'
                       WHEN usage LIKE '%ETH%' THEN 'Ethernet'
                       ELSE 'OTHER'
                   END as interface_type
            FROM gpio_af_config
            WHERE usage LIKE '%USART%' OR usage LIKE '%UART%' 
               OR usage LIKE '%SPI%' OR usage LIKE '%I2C%'
               OR usage LIKE '%CAN%' OR usage LIKE '%USB%'
               OR usage LIKE '%ETH%'
        ''')
        
        print("\n2. 通信接口分类:")
        cursor.execute('''
            SELECT interface_type, COUNT(*) as count
            FROM communication_interfaces
            GROUP BY interface_type
            ORDER BY count DESC
        ''')
        
        for row in cursor.fetchall():
            print(f"   {row[0]}: {row[1]} 个配置")

def export_reports():
    """导出报告"""
    print("\n=== 导出功能报告 ===")
    
    with GPIOAFDatabase() as db:
        # 确保数据已导入
        if os.path.exists("../excel/Q800_GPIO_AF.xlsx"):
            db.import_from_excel("Q800_GPIO_AF.xlsx")
        
        cursor = db.conn.cursor()
        
        # 导出引脚功能映射表
        print("1. 生成引脚功能映射报告...")
        cursor.execute('''
            SELECT pin_name, af_name, usage
            FROM gpio_af_config
            ORDER BY pin_name, af_name
        ''')
        
        with open("pin_function_mapping.csv", "w", encoding="utf-8") as f:
            f.write("Pin Name,AF Name,Usage\n")
            for row in cursor.fetchall():
                f.write(f"{row[0]},{row[1]},{row[2]}\n")
        
        print("   已生成: pin_function_mapping.csv")
        
        # 导出统计报告
        print("2. 生成统计报告...")
        with open("gpio_statistics.txt", "w", encoding="utf-8") as f:
            # 基本统计
            stats = db.get_statistics()
            f.write("GPIO AF配置统计报告\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"总配置数: {stats['total_configs']}\n")
            f.write(f"引脚数量: {stats['pin_count']}\n")
            f.write(f"AF数量: {stats['af_count']}\n")
            f.write(f"功能数量: {stats['function_count']}\n\n")
            
            # AF使用统计
            f.write("AF使用统计:\n")
            cursor.execute('''
                SELECT af_name, COUNT(*) as count
                FROM gpio_af_config
                GROUP BY af_name
                ORDER BY af_name
            ''')
            for row in cursor.fetchall():
                f.write(f"  {row[0]}: {row[1]} 个配置\n")
        
        print("   已生成: gpio_statistics.txt")

def main():
    """主函数"""
    print("GPIO AF数据库高级查询功能演示")
    print("=" * 50)
    
    if not os.path.exists("../excel/Q800_GPIO_AF.xlsx"):
        print("错误：找不到 Q800_GPIO_AF.xlsx 文件")
        return
    
    # 高级SQL查询
    advanced_sql_queries()
    
    # 自定义分析
    custom_analysis()
    
    # 创建自定义视图
    create_custom_views()
    
    # 导出报告
    export_reports()
    
    print("\n=== 数据库版本的优势总结 ===")
    print("1. ✓ 支持复杂的SQL查询和统计分析")
    print("2. ✓ 可以创建自定义视图和索引")
    print("3. ✓ 支持数据导出和报告生成")
    print("4. ✓ 提供更好的数据完整性保证")
    print("5. ✓ 支持事务处理和并发访问")
    print("6. ✓ 可以轻松扩展新的查询功能")
    print("7. ✓ 适合集成到更大的应用系统中")

if __name__ == "__main__":
    main()
