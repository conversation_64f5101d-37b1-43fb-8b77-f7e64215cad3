# GPIO AF查询工具方案对比总结

## 概述

本项目提供了两种GPIO AF查询方案：
1. **Excel版本** - 基于pandas直接读取Excel文件
2. **数据库版本** - 将数据导入SQLite数据库进行查询

## 方案对比

### 📊 性能对比

| 指标 | Excel版本 | 数据库版本 | 优势方 |
|------|-----------|------------|--------|
| 数据加载时间 | 0.164秒 | 0.083秒 | 数据库版本 |
| 单次查询时间 | <0.001秒 | <0.001秒 | 相当 |
| 复杂查询支持 | ❌ | ✅ | 数据库版本 |
| 内存使用 | 较高 | 较低 | 数据库版本 |
| 并发访问 | ❌ | ✅ | 数据库版本 |

### 🔧 功能对比

#### Excel版本功能
- ✅ 基本查询（引脚+功能 → AF）
- ✅ 灵活查询（单条件或多条件）
- ✅ 部分匹配查询
- ✅ 简单易用，无需额外设置
- ❌ 复杂统计查询
- ❌ 高性能索引查询
- ❌ 并发访问支持
- ❌ 数据持久化优化

#### 数据库版本功能
- ✅ 基本查询（引脚+功能 → AF）
- ✅ 灵活查询（单条件或多条件）
- ✅ 部分匹配查询
- ✅ 复杂统计查询
- ✅ 高性能索引查询
- ✅ 并发访问支持
- ✅ 数据持久化优化
- ✅ SQL查询支持
- ✅ 事务支持
- ✅ 自定义视图和报告
- ✅ 数据导出功能

### 📈 数据统计（基于Q800芯片）

- **总配置数**: 975条
- **引脚数量**: 110个
- **AF数量**: 16个（AF0-AF15）
- **功能数量**: 483种不同功能

### 🎯 使用场景建议

#### 选择Excel版本的情况：
1. **偶尔查询** - 只是偶尔需要查询GPIO配置
2. **简单需求** - 只需要基本的引脚功能查询
3. **快速原型** - 需要快速验证或测试
4. **单用户使用** - 只有一个人使用
5. **无数据库经验** - 团队对数据库不熟悉

#### 选择数据库版本的情况：
1. **频繁查询** - 需要经常查询GPIO配置
2. **复杂分析** - 需要统计分析、报告生成
3. **应用集成** - 需要集成到更大的应用系统
4. **多用户访问** - 多人同时使用
5. **性能要求** - 对查询性能有较高要求
6. **扩展需求** - 未来可能需要添加更多功能

## 🚀 快速开始

### Excel版本使用

```python
from old.get_gpio_af import load_gpio_data, find_af_by_pin_and_function

# 加载数据
gpio_data = load_gpio_data("excel/Q800_GPIO_AF.xlsx")

# 查询AF
result = find_af_by_pin_and_function(gpio_data, "PA0", "TIMER1_CH0")
print(result)  # {'pin_name': 'PA0', 'af_name': 'AF1', 'usage': 'TIMER1_CH0/TIMER1_ETI'}
```

### 数据库版本使用

```python
from old.gpio_af_database import GPIOAFDatabase

# 创建数据库并导入数据
with GPIOAFDatabase() as db:
    db.import_from_excel("Q800_GPIO_AF.xlsx")

    # 查询AF
    result = db.find_af_by_pin_and_function("PA0", "TIMER1_CH0")
    print(result)  # {'pin_name': 'PA0', 'af_name': 'AF1', 'usage': 'TIMER1_CH0/TIMER1_ETI'}
```

## 📁 文件说明

### 核心文件
- `get_gpio_af.py` - Excel版本主程序
- `gpio_af_database.py` - 数据库版本主程序
- `Q800_GPIO_AF.xlsx` - 原始数据文件

### 示例和测试
- `example_usage.py` - Excel版本使用示例
- `performance_comparison.py` - 性能对比测试
- `advanced_database_queries.py` - 数据库高级查询示例

### 文档
- `README.md` - 基本使用说明
- `方案对比总结.md` - 本文档

## 🔍 高级功能示例（数据库版本独有）

### 1. 统计分析
```python
# 查找使用频率最高的AF
cursor.execute('''
    SELECT af_name, COUNT(*) as usage_count 
    FROM gpio_af_config 
    GROUP BY af_name 
    ORDER BY usage_count DESC
''')
```

### 2. 多功能引脚查找
```python
# 查找AF配置最多的引脚
cursor.execute('''
    SELECT pin_name, COUNT(*) as af_count 
    FROM gpio_af_config 
    GROUP BY pin_name 
    ORDER BY af_count DESC 
    LIMIT 10
''')
```

### 3. 功能冲突检测
```python
# 查找支持多个UART功能的引脚
cursor.execute('''
    SELECT pin_name, GROUP_CONCAT(af_name || ':' || usage, '; ') as configs
    FROM gpio_af_config 
    WHERE usage LIKE '%UART%' OR usage LIKE '%USART%'
    GROUP BY pin_name 
    HAVING COUNT(*) > 1
''')
```

## 💡 最终建议

### 对于您的具体需求
根据您的描述"希望包含查询调件即可，例如PA0引脚和AF1对应的用途"，两种方案都能很好地满足需求。

**推荐选择**：
- 如果只是个人使用或小团队偶尔查询：**Excel版本**
- 如果需要集成到应用中或有扩展需求：**数据库版本**

### 迁移路径
可以先使用Excel版本快速满足当前需求，后续如果需要更多功能，可以无缝迁移到数据库版本，因为两个版本的API设计是兼容的。

## 📞 技术支持

两种方案都已经过充分测试，包含完整的示例代码和文档。如有问题，可以参考：
1. 各文件中的示例代码
2. 性能对比测试结果
3. 高级查询功能演示
