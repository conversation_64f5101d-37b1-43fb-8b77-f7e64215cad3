#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPIO AF数据库管理工具

将Excel数据导入SQLite数据库，提供更高效的查询功能
"""

import sqlite3
import pandas as pd
import os
from typing import List, Dict, Optional, Union

class GPIOAFDatabase:
    def __init__(self, db_path: str = "gpio_af.db"):
        """
        初始化数据库连接
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.conn = None
        self._connect()
        self._create_tables()
    
    def _connect(self):
        """建立数据库连接"""
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
    
    def _create_tables(self):
        """创建数据库表"""
        cursor = self.conn.cursor()
        
        # 创建GPIO AF配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS gpio_af_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pin_name TEXT NOT NULL,
                af_name TEXT NOT NULL,
                usage TEXT NOT NULL,
                UNIQUE(pin_name, af_name)
            )
        ''')
        
        # 创建索引以提高查询性能
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_pin_name ON gpio_af_config(pin_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_af_name ON gpio_af_config(af_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_usage ON gpio_af_config(usage)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_pin_usage ON gpio_af_config(pin_name, usage)')
        
        self.conn.commit()
    
    def import_from_excel(self, excel_path: str) -> bool:
        """
        从Excel文件导入数据到数据库
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            bool: 导入是否成功
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_path, header=0)
            af_names = df.columns.tolist()[1:]  # 获取AF列名
            
            cursor = self.conn.cursor()
            
            # 清空现有数据
            cursor.execute('DELETE FROM gpio_af_config')
            
            # 插入数据
            for _, row in df.iterrows():
                pin_name = row.iloc[0]  # 第一列是引脚名称
                
                for i, af_name in enumerate(af_names):
                    usage = row.iloc[i+1]  # AF对应的用途
                    
                    # 跳过空值
                    if pd.isna(usage) or usage == "":
                        continue
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO gpio_af_config (pin_name, af_name, usage)
                        VALUES (?, ?, ?)
                    ''', (pin_name, af_name, usage))
            
            self.conn.commit()
            
            # 获取导入的记录数
            cursor.execute('SELECT COUNT(*) FROM gpio_af_config')
            count = cursor.fetchone()[0]
            print(f"成功导入 {count} 条GPIO AF配置记录")
            
            return True
            
        except Exception as e:
            print(f"导入Excel数据时出错: {e}")
            return False
    
    def find_af_by_pin_and_function(self, pin_name: str, function_name: str) -> Optional[Dict]:
        """
        根据引脚名称和功能名称查找AF编号
        
        Args:
            pin_name: 引脚名称，如 "PA0"
            function_name: 功能名称，如 "TIMER1_CH0"
            
        Returns:
            匹配的AF信息字典，如果没找到返回None
        """
        cursor = self.conn.cursor()
        
        # 先尝试精确匹配
        cursor.execute('''
            SELECT pin_name, af_name, usage 
            FROM gpio_af_config 
            WHERE pin_name = ? AND usage = ?
        ''', (pin_name, function_name))
        
        result = cursor.fetchone()
        if result:
            return dict(result)
        
        # 如果精确匹配失败，尝试部分匹配
        cursor.execute('''
            SELECT pin_name, af_name, usage 
            FROM gpio_af_config 
            WHERE pin_name = ? AND usage LIKE ?
        ''', (pin_name, f'%{function_name}%'))
        
        result = cursor.fetchone()
        if result:
            return dict(result)
        
        return None
    
    def get_pin_all_configs(self, pin_name: str) -> List[Dict]:
        """
        获取指定引脚的所有AF配置
        
        Args:
            pin_name: 引脚名称
            
        Returns:
            AF配置列表
        """
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT pin_name, af_name, usage 
            FROM gpio_af_config 
            WHERE pin_name = ?
            ORDER BY af_name
        ''', (pin_name,))
        
        return [dict(row) for row in cursor.fetchall()]
    
    def find_pins_by_function(self, function_name: str, exact_match: bool = False) -> List[Dict]:
        """
        根据功能名称查找所有相关的引脚配置
        
        Args:
            function_name: 功能名称
            exact_match: 是否精确匹配
            
        Returns:
            匹配的配置列表
        """
        cursor = self.conn.cursor()
        
        if exact_match:
            cursor.execute('''
                SELECT pin_name, af_name, usage 
                FROM gpio_af_config 
                WHERE usage = ?
                ORDER BY pin_name, af_name
            ''', (function_name,))
        else:
            cursor.execute('''
                SELECT pin_name, af_name, usage 
                FROM gpio_af_config 
                WHERE usage LIKE ?
                ORDER BY pin_name, af_name
            ''', (f'%{function_name}%',))
        
        return [dict(row) for row in cursor.fetchall()]
    
    def get_af_all_configs(self, af_name: str) -> List[Dict]:
        """
        获取指定AF的所有配置
        
        Args:
            af_name: AF名称，如 "AF1"
            
        Returns:
            配置列表
        """
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT pin_name, af_name, usage 
            FROM gpio_af_config 
            WHERE af_name = ?
            ORDER BY pin_name
        ''', (af_name,))
        
        return [dict(row) for row in cursor.fetchall()]
    
    def search_configs(self, pin_name: str = None, af_name: str = None, 
                      function_name: str = None, exact_match: bool = False) -> List[Dict]:
        """
        灵活搜索配置
        
        Args:
            pin_name: 引脚名称（可选）
            af_name: AF名称（可选）
            function_name: 功能名称（可选）
            exact_match: 功能名称是否精确匹配
            
        Returns:
            匹配的配置列表
        """
        cursor = self.conn.cursor()
        
        conditions = []
        params = []
        
        if pin_name:
            conditions.append("pin_name = ?")
            params.append(pin_name)
        
        if af_name:
            conditions.append("af_name = ?")
            params.append(af_name)
        
        if function_name:
            if exact_match:
                conditions.append("usage = ?")
                params.append(function_name)
            else:
                conditions.append("usage LIKE ?")
                params.append(f'%{function_name}%')
        
        if not conditions:
            return []
        
        query = f'''
            SELECT pin_name, af_name, usage 
            FROM gpio_af_config 
            WHERE {' AND '.join(conditions)}
            ORDER BY pin_name, af_name
        '''
        
        cursor.execute(query, params)
        return [dict(row) for row in cursor.fetchall()]
    
    def get_statistics(self) -> Dict:
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        cursor = self.conn.cursor()
        
        # 总配置数
        cursor.execute('SELECT COUNT(*) FROM gpio_af_config')
        total_configs = cursor.fetchone()[0]
        
        # 引脚数量
        cursor.execute('SELECT COUNT(DISTINCT pin_name) FROM gpio_af_config')
        pin_count = cursor.fetchone()[0]
        
        # AF数量
        cursor.execute('SELECT COUNT(DISTINCT af_name) FROM gpio_af_config')
        af_count = cursor.fetchone()[0]
        
        # 功能数量
        cursor.execute('SELECT COUNT(DISTINCT usage) FROM gpio_af_config')
        function_count = cursor.fetchone()[0]
        
        return {
            'total_configs': total_configs,
            'pin_count': pin_count,
            'af_count': af_count,
            'function_count': function_count
        }
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def main():
    """示例用法"""
    # 创建数据库实例
    with GPIOAFDatabase() as db:
        # 从Excel导入数据
        if os.path.exists("../excel/Q800_GPIO_AF.xlsx"):
            print("正在从Excel文件导入数据...")
            success = db.import_from_excel("Q800_GPIO_AF.xlsx")
            
            if success:
                print("\n数据库统计信息:")
                stats = db.get_statistics()
                for key, value in stats.items():
                    print(f"  {key}: {value}")
                
                print("\n=== 数据库查询示例 ===")
                
                # 示例1：根据引脚和功能查询AF
                print("\n1. 根据引脚和功能查询AF:")
                result = db.find_af_by_pin_and_function("PA0", "TIMER1_CH0")
                if result:
                    print(f"   PA0 + TIMER1_CH0 -> {result['af_name']} ({result['usage']})")
                else:
                    print("   未找到匹配项")
                
                # 示例2：查询引脚的所有配置
                print("\n2. PA0引脚的所有配置:")
                configs = db.get_pin_all_configs("PA0")
                for config in configs[:5]:  # 只显示前5个
                    print(f"   {config['af_name']} -> {config['usage']}")
                if len(configs) > 5:
                    print(f"   ... 还有 {len(configs) - 5} 个配置")
                
                # 示例3：根据功能查询引脚
                print("\n3. 包含TIMER1的所有配置:")
                configs = db.find_pins_by_function("TIMER1")
                for config in configs[:5]:  # 只显示前5个
                    print(f"   {config['pin_name']} {config['af_name']} -> {config['usage']}")
                if len(configs) > 5:
                    print(f"   ... 还有 {len(configs) - 5} 个配置")
        else:
            print("错误：找不到 Q800_GPIO_AF.xlsx 文件")


if __name__ == "__main__":
    main()
