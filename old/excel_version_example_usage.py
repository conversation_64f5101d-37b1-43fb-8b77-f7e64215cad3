#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPIO AF查询工具使用示例

这个文件展示了如何使用GPIO AF查询工具的各种功能
"""

from get_gpio_af import load_gpio_data, find_af_by_pin_and_function, flexible_query

def main():
    # 加载GPIO数据
    gpio_data = load_gpio_data("../excel/Q800_GPIO_AF.xlsx")
    
    if not gpio_data:
        print("错误：无法加载GPIO数据")
        return
    
    print("=== GPIO AF查询工具使用示例 ===\n")
    
    # 示例1：根据引脚和功能查询AF（您的主要需求）
    print("1. 根据引脚名称和功能名称查询AF编号:")
    
    test_cases = [
        ("PA0", "TIMER1_CH0"),
        ("PA1", "TIMER1_CH1"), 
        ("PA2", "TIMER1_CH2"),
        ("PA3", "TIMER1_CH3"),
        ("PA0", "TIMER4_CH0"),
        ("PA0", "USART1"),
        ("PB0", "TIMER1_CH2"),
        ("PC0", "TIMER4_CH0")
    ]
    
    for pin, function in test_cases:
        result = find_af_by_pin_and_function(gpio_data, pin, function)
        
        if isinstance(result, dict) and "af_name" in result:
            print(f"   {pin:4} + {function:12} -> {result['af_name']:4} ({result['usage']})")
        elif isinstance(result, dict) and "matches" in result:
            print(f"   {pin:4} + {function:12} -> 多个匹配项:")
            for match in result["matches"]:
                print(f"       - {match['af_name']} ({match['usage']})")
        else:
            print(f"   {pin:4} + {function:12} -> 未找到")
    
    print("\n" + "="*60)
    
    # 示例2：查询特定引脚的所有AF配置
    print("\n2. 查询特定引脚的所有AF配置:")
    
    pins_to_check = ["PA0", "PA1", "PB0"]
    
    for pin in pins_to_check:
        print(f"\n   {pin} 引脚的所有AF配置:")
        results = flexible_query(gpio_data, pin_name=pin)
        
        if isinstance(results, list):
            for result in results:
                print(f"     {result['af_name']:4} -> {result['usage']}")
        else:
            print(f"     {results}")
    
    print("\n" + "="*60)
    
    # 示例3：查询特定功能的所有引脚配置
    print("\n3. 查询特定功能的所有引脚配置:")
    
    functions_to_check = ["TIMER1_CH0", "USART1_TX", "SPI0_SCK"]
    
    for function in functions_to_check:
        print(f"\n   包含 '{function}' 的所有配置:")
        results = flexible_query(gpio_data, usage=function)
        
        if isinstance(results, list):
            for result in results[:5]:  # 只显示前5个
                print(f"     {result['pin_name']:4} {result['af_name']:4} -> {result['usage']}")
            if len(results) > 5:
                print(f"     ... 还有 {len(results) - 5} 个匹配项")
        else:
            print(f"     {results}")
    
    print("\n" + "="*60)
    
    # 示例4：查询特定AF的所有配置
    print("\n4. 查询特定AF的所有配置:")
    
    afs_to_check = ["AF1", "AF7"]
    
    for af in afs_to_check:
        print(f"\n   {af} 的所有配置:")
        results = flexible_query(gpio_data, af_name=af)
        
        if isinstance(results, list):
            print(f"     共找到 {len(results)} 个配置，显示前5个:")
            for result in results[:5]:
                print(f"     {result['pin_name']:4} -> {result['usage']}")
            if len(results) > 5:
                print(f"     ... 还有 {len(results) - 5} 个配置")
        else:
            print(f"     {results}")

if __name__ == "__main__":
    main()
