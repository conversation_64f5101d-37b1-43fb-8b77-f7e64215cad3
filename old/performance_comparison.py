#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能对比测试：Excel版本 vs 数据库版本

比较两种方案的查询性能和功能差异
"""

import time
import os
from get_gpio_af import load_gpio_data, find_af_by_pin_and_function as excel_find_af
from gpio_af_database import GPIOAFDatabase

def measure_time(func, *args, **kwargs):
    """测量函数执行时间"""
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    return result, end_time - start_time

def test_excel_performance():
    """测试Excel版本性能"""
    print("=== Excel版本性能测试 ===")
    
    # 加载数据
    print("1. 数据加载测试:")
    gpio_data, load_time = measure_time(load_gpio_data, "Q800_GPIO_AF.xlsx")
    print(f"   数据加载时间: {load_time:.4f} 秒")
    
    if not gpio_data:
        print("   错误：无法加载Excel数据")
        return None, {}
    
    # 查询测试
    test_cases = [
        ("PA0", "TIMER1_CH0"),
        ("PA1", "TIMER1_CH1"),
        ("PA2", "TIMER1_CH2"),
        ("PB0", "TIMER1"),
        ("PC0", "USART1")
    ]
    
    print("\n2. 查询性能测试:")
    total_query_time = 0
    
    for pin, func in test_cases:
        result, query_time = measure_time(excel_find_af, gpio_data, pin, func)
        total_query_time += query_time
        print(f"   {pin} + {func}: {query_time:.6f} 秒")
    
    avg_query_time = total_query_time / len(test_cases)
    print(f"   平均查询时间: {avg_query_time:.6f} 秒")
    
    return gpio_data, {
        'load_time': load_time,
        'avg_query_time': avg_query_time,
        'total_query_time': total_query_time
    }

def test_database_performance():
    """测试数据库版本性能"""
    print("\n=== 数据库版本性能测试 ===")
    
    # 数据库初始化和导入
    print("1. 数据库初始化和导入测试:")
    
    def init_and_import():
        db = GPIOAFDatabase("test_performance.db")
        db.import_from_excel("Q800_GPIO_AF.xlsx")
        return db
    
    db, init_time = measure_time(init_and_import)
    print(f"   数据库初始化和导入时间: {init_time:.4f} 秒")
    
    # 查询测试
    test_cases = [
        ("PA0", "TIMER1_CH0"),
        ("PA1", "TIMER1_CH1"),
        ("PA2", "TIMER1_CH2"),
        ("PB0", "TIMER1"),
        ("PC0", "USART1")
    ]
    
    print("\n2. 查询性能测试:")
    total_query_time = 0
    
    for pin, func in test_cases:
        result, query_time = measure_time(db.find_af_by_pin_and_function, pin, func)
        total_query_time += query_time
        print(f"   {pin} + {func}: {query_time:.6f} 秒")
    
    avg_query_time = total_query_time / len(test_cases)
    print(f"   平均查询时间: {avg_query_time:.6f} 秒")
    
    # 复杂查询测试（数据库独有功能）
    print("\n3. 复杂查询测试（数据库独有）:")
    
    # 统计查询
    stats, stats_time = measure_time(db.get_statistics)
    print(f"   统计查询时间: {stats_time:.6f} 秒")
    print(f"   统计结果: {stats}")
    
    # 模糊查询
    fuzzy_results, fuzzy_time = measure_time(db.find_pins_by_function, "TIMER1")
    print(f"   模糊查询时间: {fuzzy_time:.6f} 秒")
    print(f"   模糊查询结果数量: {len(fuzzy_results)}")
    
    # 多条件查询
    multi_results, multi_time = measure_time(db.search_configs, pin_name="PA0", function_name="TIMER")
    print(f"   多条件查询时间: {multi_time:.6f} 秒")
    print(f"   多条件查询结果数量: {len(multi_results)}")
    
    db.close()
    
    return {
        'init_time': init_time,
        'avg_query_time': avg_query_time,
        'total_query_time': total_query_time,
        'stats_time': stats_time,
        'fuzzy_time': fuzzy_time,
        'multi_time': multi_time
    }

def compare_functionality():
    """功能对比"""
    print("\n=== 功能对比 ===")
    
    print("Excel版本功能:")
    print("  ✓ 基本查询（引脚+功能 -> AF）")
    print("  ✓ 灵活查询（单条件或多条件）")
    print("  ✓ 部分匹配查询")
    print("  ✗ 复杂统计查询")
    print("  ✗ 高性能索引查询")
    print("  ✗ 并发访问支持")
    print("  ✗ 数据持久化优化")
    
    print("\n数据库版本功能:")
    print("  ✓ 基本查询（引脚+功能 -> AF）")
    print("  ✓ 灵活查询（单条件或多条件）")
    print("  ✓ 部分匹配查询")
    print("  ✓ 复杂统计查询")
    print("  ✓ 高性能索引查询")
    print("  ✓ 并发访问支持")
    print("  ✓ 数据持久化优化")
    print("  ✓ SQL查询支持")
    print("  ✓ 事务支持")

def memory_usage_test():
    """内存使用测试"""
    print("\n=== 内存使用对比 ===")
    
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    
    # Excel版本内存使用
    mem_before = process.memory_info().rss / 1024 / 1024  # MB
    gpio_data = load_gpio_data("../excel/Q800_GPIO_AF.xlsx")
    mem_after_excel = process.memory_info().rss / 1024 / 1024  # MB
    excel_memory = mem_after_excel - mem_before
    
    print(f"Excel版本内存使用: {excel_memory:.2f} MB")
    
    # 数据库版本内存使用
    mem_before_db = process.memory_info().rss / 1024 / 1024  # MB
    db = GPIOAFDatabase("test_memory.db")
    db.import_from_excel("Q800_GPIO_AF.xlsx")
    mem_after_db = process.memory_info().rss / 1024 / 1024  # MB
    db_memory = mem_after_db - mem_before_db
    
    print(f"数据库版本内存使用: {db_memory:.2f} MB")
    
    db.close()
    
    # 清理测试文件
    for file in ["test_performance.db", "test_memory.db"]:
        if os.path.exists(file):
            os.remove(file)

def main():
    """主测试函数"""
    print("GPIO AF查询工具性能对比测试")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists("../excel/Q800_GPIO_AF.xlsx"):
        print("错误：找不到 Q800_GPIO_AF.xlsx 文件")
        return
    
    # Excel版本测试
    excel_data, excel_perf = test_excel_performance()
    
    # 数据库版本测试
    db_perf = test_database_performance()
    
    # 功能对比
    compare_functionality()
    
    # 内存使用测试
    try:
        memory_usage_test()
    except ImportError:
        print("\n=== 内存使用对比 ===")
        print("需要安装 psutil 库来进行内存测试: pip install psutil")
    
    # 性能总结
    print("\n=== 性能总结 ===")
    if excel_perf and db_perf:
        print(f"数据加载/初始化:")
        print(f"  Excel版本: {excel_perf['load_time']:.4f} 秒")
        print(f"  数据库版本: {db_perf['init_time']:.4f} 秒")
        
        print(f"\n平均查询时间:")
        print(f"  Excel版本: {excel_perf['avg_query_time']:.6f} 秒")
        print(f"  数据库版本: {db_perf['avg_query_time']:.6f} 秒")
        
        if db_perf['avg_query_time'] > 0:
            speedup = excel_perf['avg_query_time'] / db_perf['avg_query_time']
            print(f"  数据库版本查询速度提升: {speedup:.2f}x")
    
    print("\n=== 建议 ===")
    print("1. 如果只是偶尔查询，Excel版本已经足够")
    print("2. 如果需要频繁查询或复杂查询，建议使用数据库版本")
    print("3. 如果需要在应用程序中集成，数据库版本更适合")
    print("4. 如果需要多用户访问，必须使用数据库版本")

if __name__ == "__main__":
    main()
