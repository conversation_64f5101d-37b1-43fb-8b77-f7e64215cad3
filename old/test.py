from get_gpio_af import load_gpio_data, query_gpio_data

# 加载数据
gpio_data = load_gpio_data("../excel/Q800_GPIO_AF.xlsx")

# 查询示例：通过引脚名和用途查找AF名称
result = query_gpio_data(gpio_data, pin_name="PA0", usage="ADC_IN0")
print(result)

# 查询示例：通过引脚名和AF名称查找用途
result = query_gpio_data(gpio_data, pin_name="PA0", af_name="AF1")
print(result)

# 查询示例：通过AF名称和用途查找引脚名
result = query_gpio_data(gpio_data, af_name="AF1", usage="UART4_TX")
print(result)