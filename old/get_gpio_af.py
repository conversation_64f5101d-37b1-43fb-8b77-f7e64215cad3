import pandas as pd
import os

def load_gpio_data(file_path):
    """
    读取GPIO AF Excel文件并转换为便于查询的数据结构
    """
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return None
    
    try:
        # 读取Excel文件，跳过第一行（标题行）
        df = pd.read_excel(file_path, header=0)
        
        # 获取AF名称（第二行，从第二列开始）
        af_names = df.columns.tolist()[1:]
        
        # 创建三个字典用于不同条件的查询
        # 1. 通过引脚名和用途查找AF名称: {(pin_name, usage): af_name}
        pin_usage_to_af = {}
        # 2. 通过引脚名和AF名称查找用途: {(pin_name, af_name): usage}
        pin_af_to_usage = {}
        # 3. 通过AF名称和用途查找引脚名: {(af_name, usage): [pin_names]}
        af_usage_to_pins = {}
        
        # 遍历数据行
        for _, row in df.iterrows():
            pin_name = row.iloc[0]  # 第一列是引脚名称
            
            # 遍历每个AF列
            for i, af_name in enumerate(af_names):
                usage = row.iloc[i+1]  # AF对应的用途
                
                # 跳过空值
                if pd.isna(usage) or usage == "":
                    continue
                
                # 填充查询字典
                pin_usage_to_af[(pin_name, usage)] = af_name
                pin_af_to_usage[(pin_name, af_name)] = usage
                
                # 对于AF名称和用途到引脚名的映射，可能有多个引脚
                if (af_name, usage) not in af_usage_to_pins:
                    af_usage_to_pins[(af_name, usage)] = []
                af_usage_to_pins[(af_name, usage)].append(pin_name)
        
        return {
            "pin_usage_to_af": pin_usage_to_af,
            "pin_af_to_usage": pin_af_to_usage,
            "af_usage_to_pins": af_usage_to_pins,
            "af_names": af_names
        }
    
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def query_gpio_data(data, **kwargs):
    """
    根据提供的条件查询GPIO数据

    参数:
        data: 由load_gpio_data函数返回的数据结构
        **kwargs: 可以包含以下键值对:
            - pin_name: 引脚名称
            - af_name: AF名称
            - usage: 用途

    返回:
        根据提供的条件返回相应的查询结果
    """
    if data is None:
        return "数据未加载"

    pin_name = kwargs.get("pin_name")
    af_name = kwargs.get("af_name")
    usage = kwargs.get("usage")

    # 检查提供的条件数量
    conditions = sum(1 for x in [pin_name, af_name, usage] if x is not None)
    if conditions != 2:
        return "请提供恰好两个条件（引脚名、AF名称或用途）"

    # 根据不同的条件组合进行查询
    if pin_name and usage:
        # 通过引脚名和用途查找AF名称
        key = (pin_name, usage)
        if key in data["pin_usage_to_af"]:
            return {"af_name": data["pin_usage_to_af"][key]}
        else:
            return "未找到匹配的AF名称"

    elif pin_name and af_name:
        # 通过引脚名和AF名称查找用途
        key = (pin_name, af_name)
        if key in data["pin_af_to_usage"]:
            return {"usage": data["pin_af_to_usage"][key]}
        else:
            return "未找到匹配的用途"

    elif af_name and usage:
        # 通过AF名称和用途查找引脚名
        key = (af_name, usage)
        if key in data["af_usage_to_pins"]:
            return {"pin_names": data["af_usage_to_pins"][key]}
        else:
            return "未找到匹配的引脚名"

    return "查询条件无效"

def flexible_query(data, **kwargs):
    """
    灵活查询函数，支持单个或多个条件的查询

    参数:
        data: 由load_gpio_data函数返回的数据结构
        **kwargs: 可以包含以下键值对:
            - pin_name: 引脚名称
            - af_name: AF名称
            - usage: 用途（支持部分匹配）

    返回:
        匹配的结果列表
    """
    if data is None:
        return "数据未加载"

    pin_name = kwargs.get("pin_name")
    af_name = kwargs.get("af_name")
    usage = kwargs.get("usage")

    results = []

    # 如果只提供了引脚名称，返回该引脚的所有AF配置
    if pin_name and not af_name and not usage:
        for (p, af), u in data["pin_af_to_usage"].items():
            if p == pin_name:
                results.append({
                    "pin_name": p,
                    "af_name": af,
                    "usage": u
                })
        return results if results else f"未找到引脚 {pin_name} 的配置"

    # 如果只提供了AF名称，返回该AF的所有配置
    elif af_name and not pin_name and not usage:
        for (af, u), pins in data["af_usage_to_pins"].items():
            if af == af_name:
                for p in pins:
                    results.append({
                        "pin_name": p,
                        "af_name": af,
                        "usage": u
                    })
        return results if results else f"未找到AF {af_name} 的配置"

    # 如果只提供了用途，返回所有匹配的配置（支持部分匹配）
    elif usage and not pin_name and not af_name:
        for (p, u), af in data["pin_usage_to_af"].items():
            if usage.upper() in u.upper():
                results.append({
                    "pin_name": p,
                    "af_name": af,
                    "usage": u
                })
        return results if results else f"未找到包含 {usage} 的用途配置"

    # 如果提供了引脚名称和用途，查找对应的AF
    elif pin_name and usage and not af_name:
        # 先尝试精确匹配
        key = (pin_name, usage)
        if key in data["pin_usage_to_af"]:
            return [{
                "pin_name": pin_name,
                "af_name": data["pin_usage_to_af"][key],
                "usage": usage
            }]

        # 如果精确匹配失败，尝试部分匹配
        for (p, u), af in data["pin_usage_to_af"].items():
            if p == pin_name and usage.upper() in u.upper():
                results.append({
                    "pin_name": p,
                    "af_name": af,
                    "usage": u
                })
        return results if results else f"未找到引脚 {pin_name} 和用途 {usage} 的匹配配置"

    # 如果提供了引脚名称和AF名称，查找对应的用途
    elif pin_name and af_name and not usage:
        key = (pin_name, af_name)
        if key in data["pin_af_to_usage"]:
            return [{
                "pin_name": pin_name,
                "af_name": af_name,
                "usage": data["pin_af_to_usage"][key]
            }]
        else:
            return f"未找到引脚 {pin_name} 和AF {af_name} 的匹配配置"

    # 如果提供了AF名称和用途，查找对应的引脚
    elif af_name and usage and not pin_name:
        # 先尝试精确匹配
        key = (af_name, usage)
        if key in data["af_usage_to_pins"]:
            for p in data["af_usage_to_pins"][key]:
                results.append({
                    "pin_name": p,
                    "af_name": af_name,
                    "usage": usage
                })
            return results

        # 如果精确匹配失败，尝试部分匹配
        for (af, u), pins in data["af_usage_to_pins"].items():
            if af == af_name and usage.upper() in u.upper():
                for p in pins:
                    results.append({
                        "pin_name": p,
                        "af_name": af,
                        "usage": u
                    })
        return results if results else f"未找到AF {af_name} 和用途 {usage} 的匹配配置"

    # 如果提供了所有三个条件，验证是否匹配
    elif pin_name and af_name and usage:
        key1 = (pin_name, af_name)
        key2 = (pin_name, usage)

        if (key1 in data["pin_af_to_usage"] and
            data["pin_af_to_usage"][key1] == usage and
            key2 in data["pin_usage_to_af"] and
            data["pin_usage_to_af"][key2] == af_name):
            return [{
                "pin_name": pin_name,
                "af_name": af_name,
                "usage": usage
            }]
        else:
            return f"引脚 {pin_name}、AF {af_name} 和用途 {usage} 不匹配"

    return "请至少提供一个查询条件"

def find_af_by_pin_and_function(data, pin_name, function_name):
    """
    根据引脚名称和功能名称查找AF编号的便捷函数

    参数:
        data: 由load_gpio_data函数返回的数据结构
        pin_name: 引脚名称，如 "PA0"
        function_name: 功能名称，如 "TIMER1_CH0"（支持部分匹配）

    返回:
        匹配的AF信息，格式: {'pin_name': 'PA0', 'af_name': 'AF1', 'usage': 'TIMER1_CH0/TIMER1_ETI'}
        如果没找到匹配项，返回错误信息
    """
    if data is None:
        return "数据未加载"

    # 使用灵活查询函数
    results = flexible_query(data, pin_name=pin_name, usage=function_name)

    if isinstance(results, list) and len(results) > 0:
        if len(results) == 1:
            return results[0]
        else:
            # 如果有多个匹配项，返回所有匹配项
            return {
                "message": f"找到 {len(results)} 个匹配项",
                "matches": results
            }
    else:
        return results

# 示例用法
if __name__ == "__main__":
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "Q800_GPIO_AF.xlsx")

    # 加载数据
    gpio_data = load_gpio_data(file_path)

    if gpio_data:
        print("数据加载成功！")
        print(f"共有 {len(gpio_data['af_names'])} 个AF名称")

        print("\n=== 传统查询示例（需要恰好两个条件）===")

        # 通过引脚名和用途查找AF名称
        result1 = query_gpio_data(gpio_data, pin_name="PA0", usage="TIMER1_CH0/TIMER1_ETI")
        print(f"PA0引脚和TIMER1_CH0/TIMER1_ETI用途对应的AF名称: {result1}")

        # 通过引脚名和AF名称查找用途
        result2 = query_gpio_data(gpio_data, pin_name="PA0", af_name="AF1")
        print(f"PA0引脚和AF1对应的用途: {result2}")

        print("\n=== 灵活查询示例（支持单个或多个条件）===")

        # 1. 根据引脚名称查询所有AF配置
        print("\n1. 查询PA0引脚的所有AF配置:")
        result3 = flexible_query(gpio_data, pin_name="PA0")
        if isinstance(result3, list):
            for item in result3:
                print(f"   {item}")
        else:
            print(f"   {result3}")

        # 2. 根据引脚名称和功能名称查询AF（您的需求）
        print("\n2. 根据PA0和TIMER1_CH0查询AF:")
        result4 = flexible_query(gpio_data, pin_name="PA0", usage="TIMER1_CH0")
        if isinstance(result4, list):
            for item in result4:
                print(f"   引脚: {item['pin_name']}, AF: {item['af_name']}, 用途: {item['usage']}")
        else:
            print(f"   {result4}")

        # 3. 根据功能名称查询所有相关配置
        print("\n3. 查询所有包含TIMER1的配置:")
        result5 = flexible_query(gpio_data, usage="TIMER1")
        if isinstance(result5, list):
            print(f"   找到 {len(result5)} 个匹配项:")
            for item in result5[:5]:  # 只显示前5个
                print(f"   引脚: {item['pin_name']}, AF: {item['af_name']}, 用途: {item['usage']}")
            if len(result5) > 5:
                print(f"   ... 还有 {len(result5) - 5} 个匹配项")
        else:
            print(f"   {result5}")

        # 4. 根据AF名称查询所有配置
        print("\n4. 查询AF1的所有配置:")
        result6 = flexible_query(gpio_data, af_name="AF1")
        if isinstance(result6, list):
            print(f"   找到 {len(result6)} 个匹配项:")
            for item in result6[:5]:  # 只显示前5个
                print(f"   引脚: {item['pin_name']}, AF: {item['af_name']}, 用途: {item['usage']}")
            if len(result6) > 5:
                print(f"   ... 还有 {len(result6) - 5} 个匹配项")
        else:
            print(f"   {result6}")

        print("\n=== 便捷查询函数示例（您的需求场景）===")

        # 5. 使用便捷函数查询
        print("\n5. 使用便捷函数查询PA0和TIMER1_CH0对应的AF:")
        result7 = find_af_by_pin_and_function(gpio_data, "PA0", "TIMER1_CH0")
        if isinstance(result7, dict) and "af_name" in result7:
            print(f"   结果: 引脚 {result7['pin_name']} 的功能 {result7['usage']} 对应 {result7['af_name']}")
        else:
            print(f"   {result7}")

        # 6. 再测试几个例子
        test_cases = [
            ("PA1", "TIMER1_CH1"),
            ("PA2", "TIMER1_CH2"),
            ("PB0", "TIMER1"),
            ("PC0", "TIMER1")
        ]

        print("\n6. 批量测试查询:")
        for pin, func in test_cases:
            result = find_af_by_pin_and_function(gpio_data, pin, func)
            if isinstance(result, dict) and "af_name" in result:
                print(f"   {pin} + {func} -> {result['af_name']} ({result['usage']})")
            elif isinstance(result, dict) and "matches" in result:
                print(f"   {pin} + {func} -> 找到多个匹配项:")
                for match in result["matches"][:2]:  # 只显示前2个
                    print(f"     - {match['af_name']} ({match['usage']})")
            else:
                print(f"   {pin} + {func} -> {result}")

    else:
        print("数据加载失败！")