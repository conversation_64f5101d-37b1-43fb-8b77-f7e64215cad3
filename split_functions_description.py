import pandas as pd
import re
import os
from openpyxl import load_workbook
from openpyxl.styles import Font, Alignment

def split_functions_description(input_file, output_file):
    """
    将Excel文件中的Functions description列拆分成Default、Alternate和Additional三列
    并删除拆分后各列内容中的空格
    """
    try:
        # 读取Excel文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_excel(input_file)
        
        # 显示原始列名
        print("原始列名:", df.columns.tolist())
        
        # 查找Functions description列（可能有不同的名称）
        func_desc_col = None
        for col in df.columns:
            if 'function' in col.lower() and 'description' in col.lower():
                func_desc_col = col
                break
        
        if func_desc_col is None:
            print("未找到Functions description列，请检查列名")
            return False
        
        print(f"找到Functions description列: {func_desc_col}")
        
        # 创建新的列
        df['Default'] = ''
        df['Alternate'] = ''
        df['Additional'] = ''
        
        # 处理每一行
        for index, row in df.iterrows():
            func_desc = str(row[func_desc_col])

            if pd.isna(func_desc) or func_desc == 'nan':
                continue

            # 初始化各部分
            default_part = ''
            alternate_part = ''
            additional_part = ''

            # 使用正则表达式分割，考虑换行符
            # 查找Default部分
            default_match = re.search(r'Default:\s*([^\n]*?)(?=\nAlternate:|\nAdditional:|$)', func_desc, re.DOTALL)
            if default_match:
                default_part = default_match.group(1).strip()

            # 查找Alternate部分
            alternate_match = re.search(r'Alternate:\s*(.*?)(?=\nAdditional:|$)', func_desc, re.DOTALL)
            if alternate_match:
                alternate_part = alternate_match.group(1).strip()

            # 查找Additional部分
            additional_match = re.search(r'Additional:\s*(.*?)$', func_desc, re.DOTALL)
            if additional_match:
                additional_part = additional_match.group(1).strip()

            # 删除各部分内容中的空格和换行符（保留逗号分隔）
            if default_part:
                # 移除所有空白字符（空格、换行符、制表符等）
                default_part = re.sub(r'\s+', '', default_part)
            if alternate_part:
                # 移除所有空白字符，但保留逗号后的分隔
                alternate_part = re.sub(r'\s+', '', alternate_part)
            if additional_part:
                # 移除所有空白字符，但保留逗号后的分隔
                additional_part = re.sub(r'\s+', '', additional_part)

            # 赋值到新列
            df.at[index, 'Default'] = default_part
            df.at[index, 'Alternate'] = alternate_part
            df.at[index, 'Additional'] = additional_part
        
        # 保存到新文件
        print(f"正在保存到文件: {output_file}")
        df.to_excel(output_file, index=False)

        # 设置字体为Arial和自动换行
        print("正在设置字体为Arial和自动换行...")
        wb = load_workbook(output_file)
        ws = wb.active

        # 创建Arial字体样式和自动换行对齐方式
        arial_font = Font(name='Arial')
        wrap_alignment = Alignment(wrap_text=True, vertical='top')

        # 应用Arial字体和自动换行到所有单元格
        for row in ws.iter_rows():
            for cell in row:
                cell.font = arial_font
                cell.alignment = wrap_alignment

        # 调整行高以适应自动换行的内容
        print("正在调整行高...")
        for row_num in range(1, ws.max_row + 1):
            # 设置最小行高，让自动换行能够正常显示
            ws.row_dimensions[row_num].height = None  # 让Excel自动计算行高

        # 调整列宽以更好地显示内容
        print("正在调整列宽...")
        column_widths = {
            'A': 15,  # Pin Name
            'B': 10,  # Pins
            'C': 12,  # Pin Type
            'D': 12,  # I/O Level
            'E': 50,  # Functions description (原始列，需要较宽)
            'F': 15,  # Usage
            'G': 15,  # PCB_NAME
            'H': 20,  # Note
            'I': 15,  # Default
            'J': 60,  # Alternate (通常内容较多)
            'K': 25,  # Additional
        }

        for col_letter, width in column_widths.items():
            ws.column_dimensions[col_letter].width = width

        # 保存修改后的文件
        wb.save(output_file)
        print("字体和格式设置完成！")
        print("处理完成！")
        
        # 显示处理结果统计
        non_empty_default = df['Default'].astype(str).str.len() > 0
        non_empty_alternate = df['Alternate'].astype(str).str.len() > 0
        non_empty_additional = df['Additional'].astype(str).str.len() > 0
        
        print(f"\n处理统计:")
        print(f"总行数: {len(df)}")
        print(f"有Default内容的行数: {non_empty_default.sum()}")
        print(f"有Alternate内容的行数: {non_empty_alternate.sum()}")
        print(f"有Additional内容的行数: {non_empty_additional.sum()}")
        
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return False

def main():
    # 输入和输出文件路径
    input_file = "excel/GD32H757ZxT_Datasheet_GPIO.xlsx"
    output_file = "excel/GD32H757ZxT_Datasheet_GPIO_Split.xlsx"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"输入文件不存在: {input_file}")
        return
    
    # 执行拆分
    success = split_functions_description(input_file, output_file)
    
    if success:
        print(f"\n拆分完成！结果已保存到: {output_file}")
    else:
        print("拆分失败，请检查错误信息")

if __name__ == "__main__":
    main()
