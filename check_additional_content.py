import pandas as pd

def check_additional_content():
    """检查真正包含Additional内容的行"""
    try:
        # 读取处理后的文件
        df = pd.read_excel("excel/GD32H757ZxT_Datasheet_GPIO_Split.xlsx")
        
        # 查找真正包含Additional内容的行（不是nan或空字符串）
        additional_rows = df[
            (df['Additional'].notna()) & 
            (df['Additional'] != 'nan') & 
            (df['Additional'].astype(str).str.len() > 0) &
            (df['Additional'] != '')
        ]
        
        print(f"真正包含Additional内容的行数: {len(additional_rows)}")
        
        if len(additional_rows) > 0:
            print("\n所有包含Additional内容的行:")
            print("=" * 80)
            for i, (idx, row) in enumerate(additional_rows.iterrows()):
                print(f"行 {idx+1}:")
                print(f"  Pin Name: {row['Pin Name']}")
                print(f"  Default: {row['Default']}")
                print(f"  Alternate: {row['Alternate']}")
                print(f"  Additional: {row['Additional']}")
                print("-" * 40)
        
        # 也检查原始数据中的Additional内容
        print("\n检查原始数据中的Additional内容:")
        original_df = pd.read_excel("excel/GD32H757ZxT_Datasheet_GPIO.xlsx")
        
        additional_in_original = 0
        for i, row in original_df.iterrows():
            func_desc = str(row['Functions description'])
            if 'Additional:' in func_desc:
                additional_in_original += 1
                if additional_in_original <= 5:  # 只显示前5个
                    print(f"\n原始行 {i+1}:")
                    print(f"Pin Name: {row['Pin Name']}")
                    print(f"Functions description: {func_desc}")
        
        print(f"\n原始数据中包含'Additional:'的行数: {additional_in_original}")
        
    except Exception as e:
        print(f"检查过程中出现错误: {str(e)}")

if __name__ == "__main__":
    check_additional_content()
