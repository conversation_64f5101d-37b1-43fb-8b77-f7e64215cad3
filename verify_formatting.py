from openpyxl import load_workbook

def verify_formatting():
    """验证Excel文件的格式设置"""
    try:
        # 加载处理后的文件
        wb = load_workbook("excel/GD32H757ZxT_Datasheet_GPIO_Split.xlsx")
        ws = wb.active
        
        print("验证Excel格式设置:")
        print("=" * 70)
        
        # 检查字体和自动换行设置
        print("字体和自动换行检查:")
        print("-" * 40)
        
        # 检查几个重要单元格
        test_cells = ['A1', 'E1', 'E2', 'I1', 'J1', 'K1', 'E5', 'J5']
        
        arial_count = 0
        wrap_count = 0
        total_cells = len(test_cells)
        
        for cell_addr in test_cells:
            try:
                cell = ws[cell_addr]
                
                # 检查字体
                font_name = cell.font.name if cell.font.name else "默认"
                font_status = "✅" if font_name == 'Arial' else "❌"
                if font_name == 'Arial':
                    arial_count += 1
                
                # 检查自动换行
                wrap_text = cell.alignment.wrap_text if cell.alignment else False
                wrap_status = "✅" if wrap_text else "❌"
                if wrap_text:
                    wrap_count += 1
                
                # 获取单元格值（截断显示）
                value = str(cell.value)
                if len(value) > 30:
                    value = value[:30] + "..."
                
                print(f"单元格 {cell_addr:4} | 字体: {font_name:8} {font_status} | 自动换行: {wrap_text:5} {wrap_status} | 内容: {value}")
                
            except Exception as e:
                print(f"单元格 {cell_addr}: 检查失败 - {str(e)}")
        
        print(f"\n统计结果:")
        print(f"Arial字体覆盖率: {arial_count}/{total_cells} ({arial_count/total_cells*100:.1f}%)")
        print(f"自动换行覆盖率: {wrap_count}/{total_cells} ({wrap_count/total_cells*100:.1f}%)")
        
        # 检查列宽设置
        print(f"\n列宽设置检查:")
        print("-" * 40)
        
        expected_widths = {
            'A': 15, 'B': 10, 'C': 12, 'D': 12, 'E': 50,
            'F': 15, 'G': 15, 'H': 20, 'I': 15, 'J': 60, 'K': 25
        }
        
        for col_letter, expected_width in expected_widths.items():
            actual_width = ws.column_dimensions[col_letter].width
            status = "✅" if actual_width == expected_width else "❌"
            
            # 获取列标题
            header_cell = ws[f'{col_letter}1']
            header_value = str(header_cell.value) if header_cell.value else "无标题"
            if len(header_value) > 20:
                header_value = header_value[:20] + "..."
            
            print(f"列 {col_letter} ({header_value:20}) | 期望宽度: {expected_width:2} | 实际宽度: {actual_width:2} {status}")
        
        # 检查特定的Functions description列内容
        print(f"\n Functions description列内容检查:")
        print("-" * 40)
        
        # 检查几行包含换行符的内容
        for row_num in [2, 3, 4]:  # 检查前几行数据
            cell = ws[f'E{row_num}']
            content = str(cell.value) if cell.value else ""
            
            has_newline = '\n' in content
            newline_status = "✅" if has_newline else "❌"
            
            # 显示内容的前50个字符
            display_content = content.replace('\n', '\\n')
            if len(display_content) > 50:
                display_content = display_content[:50] + "..."
            
            print(f"行 {row_num} | 包含换行符: {has_newline:5} {newline_status} | 内容: {display_content}")
        
        print(f"\n文件整体信息:")
        print(f"工作表名称: {ws.title}")
        print(f"总行数: {ws.max_row}")
        print(f"总列数: {ws.max_column}")
        
        # 检查新增列的内容
        print(f"\n新增列内容检查:")
        print("-" * 40)
        
        for row_num in [2, 3]:  # 检查前两行数据
            default_val = ws[f'I{row_num}'].value
            alternate_val = str(ws[f'J{row_num}'].value)[:40] + "..." if ws[f'J{row_num}'].value and len(str(ws[f'J{row_num}'].value)) > 40 else ws[f'J{row_num}'].value
            additional_val = ws[f'K{row_num}'].value
            
            print(f"行 {row_num}:")
            print(f"  Default: {default_val}")
            print(f"  Alternate: {alternate_val}")
            print(f"  Additional: {additional_val}")
        
    except Exception as e:
        print(f"验证过程中出现错误: {str(e)}")

if __name__ == "__main__":
    verify_formatting()
