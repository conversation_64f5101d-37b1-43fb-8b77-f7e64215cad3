import pandas as pd

def verify_results():
    """验证拆分结果"""
    try:
        # 读取处理后的文件
        df = pd.read_excel("excel/GD32H757ZxT_Datasheet_GPIO_Split.xlsx")
        
        print("验证拆分结果:")
        print("=" * 80)
        
        # 显示前几行的拆分结果
        print("前5行的拆分结果:")
        for i in range(min(5, len(df))):
            print(f"\n行 {i+1}:")
            print(f"Pin Name: {df.iloc[i]['Pin Name']}")
            print(f"Default: {df.iloc[i]['Default']}")
            print(f"Alternate: {df.iloc[i]['Alternate']}")
            print(f"Additional: {df.iloc[i]['Additional']}")
            print("-" * 40)
        
        # 查找包含Additional内容的行
        additional_rows = df[df['Additional'].astype(str).str.len() > 0]
        print(f"\n包含Additional内容的行数: {len(additional_rows)}")
        
        if len(additional_rows) > 0:
            print("\n前3个包含Additional内容的示例:")
            for i, (idx, row) in enumerate(additional_rows.head(3).iterrows()):
                print(f"示例 {i+1} (行{idx+1}):")
                print(f"  Pin Name: {row['Pin Name']}")
                print(f"  Default: {row['Default']}")
                print(f"  Alternate: {row['Alternate']}")
                print(f"  Additional: {row['Additional']}")
                print()
        
        # 检查是否有空格残留
        print("检查空格清理情况:")
        has_spaces_default = df['Default'].astype(str).str.contains(' ').sum()
        has_spaces_alternate = df['Alternate'].astype(str).str.contains(' ').sum()
        has_spaces_additional = df['Additional'].astype(str).str.contains(' ').sum()
        
        print(f"Default列中仍包含空格的行数: {has_spaces_default}")
        print(f"Alternate列中仍包含空格的行数: {has_spaces_alternate}")
        print(f"Additional列中仍包含空格的行数: {has_spaces_additional}")
        
        # 显示列信息
        print(f"\n文件信息:")
        print(f"总列数: {len(df.columns)}")
        print(f"列名: {df.columns.tolist()}")
        
    except Exception as e:
        print(f"验证过程中出现错误: {str(e)}")

if __name__ == "__main__":
    verify_results()
