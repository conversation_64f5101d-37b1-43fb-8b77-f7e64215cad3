from openpyxl import load_workbook

def verify_font():
    """验证Excel文件的字体设置"""
    try:
        # 加载处理后的文件
        wb = load_workbook("excel/GD32H757ZxT_Datasheet_GPIO_Split.xlsx")
        ws = wb.active
        
        print("验证字体设置:")
        print("=" * 60)
        
        # 检查前几个单元格的字体
        print("前10个单元格的字体检查:")
        cell_count = 0
        arial_count = 0
        
        for row_num in range(1, min(6, ws.max_row + 1)):  # 检查前5行
            for col_num in range(1, min(6, ws.max_column + 1)):  # 检查前5列
                cell = ws.cell(row=row_num, column=col_num)
                cell_count += 1
                font_name = cell.font.name if cell.font.name else "默认"
                
                if font_name == 'Arial':
                    arial_count += 1
                    status = "✅"
                else:
                    status = "❌"
                
                print(f"单元格 {cell.coordinate}: 字体={font_name} {status}")
                
                if cell_count >= 10:
                    break
            if cell_count >= 10:
                break
        
        print(f"\n字体统计:")
        print(f"检查的单元格数: {cell_count}")
        print(f"使用Arial字体的单元格数: {arial_count}")
        print(f"Arial字体覆盖率: {arial_count/cell_count*100:.1f}%")
        
        # 检查特定的重要单元格
        print(f"\n重要单元格字体检查:")
        important_cells = ['A1', 'E1', 'I1', 'J1', 'K1']  # 标题行的一些重要列
        
        for cell_addr in important_cells:
            try:
                cell = ws[cell_addr]
                font_name = cell.font.name if cell.font.name else "默认"
                value = cell.value
                status = "✅" if font_name == 'Arial' else "❌"
                print(f"单元格 {cell_addr} ('{value}'): 字体={font_name} {status}")
            except:
                print(f"单元格 {cell_addr}: 无法访问")
        
        # 随机检查一些数据单元格
        print(f"\n数据区域字体检查:")
        data_cells = ['A5', 'E5', 'I5', 'J5', 'K5']  # 数据行的一些单元格
        
        for cell_addr in data_cells:
            try:
                cell = ws[cell_addr]
                font_name = cell.font.name if cell.font.name else "默认"
                value = str(cell.value)[:20] + "..." if cell.value and len(str(cell.value)) > 20 else cell.value
                status = "✅" if font_name == 'Arial' else "❌"
                print(f"单元格 {cell_addr} ('{value}'): 字体={font_name} {status}")
            except:
                print(f"单元格 {cell_addr}: 无法访问")
        
        print(f"\n文件信息:")
        print(f"工作表名称: {ws.title}")
        print(f"总行数: {ws.max_row}")
        print(f"总列数: {ws.max_column}")
        
    except Exception as e:
        print(f"验证过程中出现错误: {str(e)}")

if __name__ == "__main__":
    verify_font()
